<template>
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" @click="$emit('close')">
    <div class="bg-white dark:bg-black border border-gray-200 dark:border-gray-800 rounded-xl shadow-xl w-full max-w-2xl max-h-[80vh] overflow-hidden" @click.stop>
      <!-- 头部 -->
      <div class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
        <h2 class="text-xl font-semibold text-black dark:text-white">房间管理</h2>
        <button
          @click="$emit('close')"
          class="p-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors"
        >
          <svg class="h-5 w-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <!-- 内容 -->
      <div class="p-6 overflow-y-auto max-h-[calc(80vh-120px)]">
        <!-- 房间信息 -->
        <div class="mb-6">
          <h3 class="text-lg font-medium text-black dark:text-white mb-4">房间信息</h3>
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">房间名称</label>
              <input
                v-model="roomName"
                type="text"
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-black dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="输入房间名称"
              />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">房间密码</label>
              <input
                v-model="roomPassword"
                type="password"
                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-black dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="设置房间密码，留空则无密码"
              />
            </div>
            <button
              @click="updateRoomInfo"
              :disabled="updating"
              class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              {{ updating ? '更新中...' : '更新房间信息' }}
            </button>
          </div>
        </div>

        <!-- 房间成员 -->
        <div>
          <h3 class="text-lg font-medium text-black dark:text-white mb-4">房间成员</h3>
          <div class="space-y-2">
            <div
              v-for="member in members"
              :key="member.userId"
              class="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-700 rounded-lg"
            >
              <div class="flex items-center gap-3">
                <div class="text-sm font-medium text-black dark:text-white">
                  {{ member.userName }}
                </div>
                <span
                  class="px-2 py-1 text-xs rounded-full"
                  :class="member.role === 'admin' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200'"
                >
                  {{ member.role === 'admin' ? '管理员' : '成员' }}
                </span>
                <span
                  v-if="member.status === 'blocked'"
                  class="px-2 py-1 text-xs rounded-full bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
                >
                  已拉黑
                </span>
              </div>
              <div class="flex gap-2" v-if="member.role !== 'admin'">
                <button
                  @click="kickUser(member.userId)"
                  class="px-3 py-1 text-xs bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200 rounded hover:bg-yellow-200 dark:hover:bg-yellow-800 transition-colors"
                >
                  踢出
                </button>
                <button
                  @click="blockUser(member.userId)"
                  :disabled="member.status === 'blocked'"
                  class="px-3 py-1 text-xs bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200 rounded hover:bg-red-200 dark:hover:bg-red-800 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  {{ member.status === 'blocked' ? '已拉黑' : '拉黑' }}
                </button>
              </div>
            </div>
            
            <!-- 空状态 -->
            <div v-if="members.length === 0" class="text-center py-6 text-gray-500 dark:text-gray-400 text-sm">
              暂无成员
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { Message } from '@arco-design/web-vue'
import { updateRoom, kickUser as kickUserAPI, blockUser as blockUserAPI, getRoomMembers } from '@/api'
import type { RoomMemberInfo } from '@/types/room'

interface Props {
  roomUuid: string
  initialName: string
}

const props = defineProps<Props>()
const emit = defineEmits<{
  close: []
  updated: []
}>()

const roomName = ref(props.initialName)
const roomPassword = ref('')
const updating = ref(false)
const members = ref<RoomMemberInfo[]>([])

// 获取房间成员
const fetchMembers = async () => {
  try {
    const response = await getRoomMembers(props.roomUuid)
    if (response.code === 0) {
      members.value = response.data
    }
  } catch (error: any) {
    console.error('获取房间成员失败:', error)
    Message.error('获取房间成员失败')
  }
}

// 更新房间信息
const updateRoomInfo = async () => {
  if (!roomName.value.trim()) {
    Message.error('请输入房间名称')
    return
  }

  try {
    updating.value = true
    const response = await updateRoom(props.roomUuid, {
      name: roomName.value.trim(),
      password: roomPassword.value.trim() || undefined
    })
    
    if (response.code === 0) {
      Message.success('房间信息更新成功')
      emit('updated')
    } else {
      Message.error(response.message || '更新失败')
    }
  } catch (error: any) {
    console.error('更新房间信息失败:', error)
    Message.error(error.response?.data?.message || '更新失败')
  } finally {
    updating.value = false
  }
}

// 踢出用户
const kickUser = async (userId: number) => {
  try {
    const response = await kickUserAPI(props.roomUuid, { userId })
    if (response.code === 0) {
      Message.success('用户已被踢出')
      await fetchMembers() // 刷新成员列表
    } else {
      Message.error(response.message || '踢出失败')
    }
  } catch (error: any) {
    console.error('踢出用户失败:', error)
    Message.error(error.response?.data?.message || '踢出失败')
  }
}

// 拉黑用户
const blockUser = async (userId: number) => {
  try {
    const response = await blockUserAPI(props.roomUuid, { userId })
    if (response.code === 0) {
      Message.success('用户已被拉黑')
      await fetchMembers() // 刷新成员列表
    } else {
      Message.error(response.message || '拉黑失败')
    }
  } catch (error: any) {
    console.error('拉黑用户失败:', error)
    Message.error(error.response?.data?.message || '拉黑失败')
  }
}

onMounted(() => {
  fetchMembers()
})
</script>
