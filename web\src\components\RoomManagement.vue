<template>
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4" @click="$emit('close')">
    <div class="bg-white dark:bg-black border border-gray-200 dark:border-gray-800 rounded-xl shadow-lg w-full max-w-2xl max-h-[90vh] overflow-hidden" @click.stop>
      <!-- 模态框内容 -->
      <div class="p-4 sm:p-6 max-h-[90vh] overflow-y-auto">
        <!-- 标题区域 -->
        <div class="text-center mb-4 sm:mb-6">
          <div
            class="inline-flex items-center justify-center w-10 h-10 sm:w-12 sm:h-12 rounded-full bg-blue-600 mb-3 sm:mb-4 transition-colors"
          >
            <CogIcon class="h-5 w-5 sm:h-6 sm:w-6 text-white" />
          </div>
          <h2 class="text-lg sm:text-xl font-bold mb-1 sm:mb-2 text-black dark:text-white">
            房间管理
          </h2>
          <p class="text-gray-600 dark:text-gray-400 text-xs sm:text-sm leading-relaxed px-2 sm:px-0">
            管理房间设置和成员权限
          </p>
        </div>
        <!-- 房间信息 -->
        <div class="mb-6 sm:mb-8">
          <div class="flex items-center gap-2 mb-4">
            <div class="w-6 h-6 rounded-lg bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
              <HomeIcon class="h-3.5 w-3.5 text-blue-600 dark:text-blue-400" />
            </div>
            <h3 class="text-base sm:text-lg font-semibold text-black dark:text-white">房间信息</h3>
          </div>

          <div class="space-y-4">
            <div>
              <label class="block text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                房间名称
              </label>
              <input
                v-model="roomName"
                type="text"
                class="w-full px-3 py-2.5 border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-800 text-black dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all text-sm"
                placeholder="输入房间名称"
              />
            </div>
            <div>
              <label class="block text-xs sm:text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                房间密码
              </label>
              <input
                v-model="roomPassword"
                type="password"
                class="w-full px-3 py-2.5 border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-800 text-black dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all text-sm"
                placeholder="设置房间密码，留空则无密码"
              />
            </div>
          </div>
        </div>

        <!-- 房间成员 -->
        <div class="mb-6">
          <div class="flex items-center gap-2 mb-4">
            <div class="w-6 h-6 rounded-lg bg-green-100 dark:bg-green-900 flex items-center justify-center">
              <UsersIcon class="h-3.5 w-3.5 text-green-600 dark:text-green-400" />
            </div>
            <h3 class="text-base sm:text-lg font-semibold text-black dark:text-white">房间成员</h3>
          </div>

          <div class="space-y-3">
            <div
              v-for="member in members"
              :key="member.userId"
              class="flex items-center justify-between p-3 sm:p-4 bg-gray-50 dark:bg-gray-900 rounded-lg border border-gray-200 dark:border-gray-700 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
            >
              <div class="flex items-center gap-3 min-w-0 flex-1">
                <div class="w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center flex-shrink-0">
                  <span class="text-white text-xs font-medium">
                    {{ member.userName.charAt(0).toUpperCase() }}
                  </span>
                </div>
                <div class="min-w-0 flex-1">
                  <div class="text-sm font-medium text-black dark:text-white truncate">
                    {{ member.userName }}
                  </div>
                  <div class="flex items-center gap-2 mt-1">
                    <span
                      class="px-2 py-0.5 text-xs rounded-full font-medium"
                      :class="member.role === 'admin'
                        ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300'
                        : 'bg-gray-100 text-gray-700 dark:bg-gray-800 dark:text-gray-300'"
                    >
                      {{ member.role === 'admin' ? '管理员' : '成员' }}
                    </span>
                    <span
                      v-if="member.status === 'blocked'"
                      class="px-2 py-0.5 text-xs rounded-full font-medium bg-red-100 text-red-700 dark:bg-red-900 dark:text-red-300"
                    >
                      已拉黑
                    </span>
                  </div>
                </div>
              </div>

              <div class="flex gap-2 ml-3" v-if="member.role !== 'admin'">
                <button
                  @click="kickUser(member.userId)"
                  class="px-3 py-1.5 text-xs font-medium bg-yellow-100 text-yellow-700 dark:bg-yellow-900 dark:text-yellow-300 rounded-md hover:bg-yellow-200 dark:hover:bg-yellow-800 transition-colors"
                >
                  踢出
                </button>
                <button
                  @click="blockUser(member.userId)"
                  :disabled="member.status === 'blocked'"
                  class="px-3 py-1.5 text-xs font-medium bg-red-100 text-red-700 dark:bg-red-900 dark:text-red-300 rounded-md hover:bg-red-200 dark:hover:bg-red-800 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  {{ member.status === 'blocked' ? '已拉黑' : '拉黑' }}
                </button>
              </div>
            </div>

            <!-- 空状态 -->
            <div v-if="members.length === 0" class="text-center py-8">
              <div class="w-12 h-12 rounded-full bg-gray-100 dark:bg-gray-800 flex items-center justify-center mx-auto mb-3">
                <UsersIcon class="h-6 w-6 text-gray-400" />
              </div>
              <p class="text-gray-500 dark:text-gray-400 text-sm">暂无成员</p>
            </div>
          </div>
        </div>

        <!-- 按钮区域 -->
        <div class="flex flex-col sm:flex-row gap-3 pt-4 border-t border-gray-200 dark:border-gray-700">
          <button
            @click="updateRoomInfo"
            :disabled="updating"
            class="flex-1 py-2.5 px-4 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-all flex items-center justify-center gap-2 order-2 sm:order-1 text-sm disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <CheckIcon v-if="!updating" class="h-4 w-4" />
            <div v-else class="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
            {{ updating ? '更新中...' : '保存更改' }}
          </button>
          <button
            @click="$emit('close')"
            class="flex-1 px-4 py-2.5 border border-gray-200 dark:border-gray-700 text-black dark:text-white font-medium rounded-lg hover:bg-gray-50 dark:hover:bg-gray-900 transition-all order-1 sm:order-2 text-sm flex items-center justify-center gap-2"
          >
            <XMarkIcon class="h-4 w-4" />
            关闭
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { Message } from '@arco-design/web-vue'
import { updateRoom, kickUser as kickUserAPI, blockUser as blockUserAPI, getRoomMembers } from '@/api'
import type { RoomMemberInfo } from '@/types/room'
import {
  CogIcon,
  HomeIcon,
  UsersIcon,
  CheckIcon,
  XMarkIcon
} from '@heroicons/vue/24/outline'

interface Props {
  roomUuid: string
  initialName: string
}

const props = defineProps<Props>()
const emit = defineEmits<{
  close: []
  updated: []
}>()

const roomName = ref(props.initialName)
const roomPassword = ref('')
const updating = ref(false)
const members = ref<RoomMemberInfo[]>([])

// 获取房间成员
const fetchMembers = async () => {
  try {
    const response = await getRoomMembers(props.roomUuid)
    if (response.code === 0) {
      members.value = response.data
    }
  } catch (error: any) {
    console.error('获取房间成员失败:', error)
    Message.error('获取房间成员失败')
  }
}

// 更新房间信息
const updateRoomInfo = async () => {
  if (!roomName.value.trim()) {
    Message.error('请输入房间名称')
    return
  }

  try {
    updating.value = true
    const response = await updateRoom(props.roomUuid, {
      name: roomName.value.trim(),
      password: roomPassword.value.trim() || undefined
    })
    
    if (response.code === 0) {
      Message.success('房间信息更新成功')
      emit('updated')
    } else {
      Message.error(response.message || '更新失败')
    }
  } catch (error: any) {
    console.error('更新房间信息失败:', error)
    Message.error(error.response?.data?.message || '更新失败')
  } finally {
    updating.value = false
  }
}

// 踢出用户
const kickUser = async (userId: number) => {
  try {
    const response = await kickUserAPI(props.roomUuid, { userId })
    if (response.code === 0) {
      Message.success('用户已被踢出')
      await fetchMembers() // 刷新成员列表
    } else {
      Message.error(response.message || '踢出失败')
    }
  } catch (error: any) {
    console.error('踢出用户失败:', error)
    Message.error(error.response?.data?.message || '踢出失败')
  }
}

// 拉黑用户
const blockUser = async (userId: number) => {
  try {
    const response = await blockUserAPI(props.roomUuid, { userId })
    if (response.code === 0) {
      Message.success('用户已被拉黑')
      await fetchMembers() // 刷新成员列表
    } else {
      Message.error(response.message || '拉黑失败')
    }
  } catch (error: any) {
    console.error('拉黑用户失败:', error)
    Message.error(error.response?.data?.message || '拉黑失败')
  }
}

onMounted(() => {
  fetchMembers()
})
</script>
