package entity

import (
	"gorm.io/gorm"
	"time"
)

// RoomUserRole 房间用户角色
type RoomUserRole string

const (
	RoomUserRoleAdmin  RoomUserRole = "admin"  // 管理员
	RoomUserRoleMember RoomUserRole = "member" // 成员
)

// RoomUserStatus 房间用户状态
type RoomUserStatus string

const (
	RoomUserStatusNormal  RoomUserStatus = "normal"  // 正常
	RoomUserStatusBlocked RoomUserStatus = "blocked" // 被拉黑
)

// RoomUser 房间用户关联表
type RoomUser struct {
	Id        uint             `gorm:"primarykey" json:"-"`
	RoomId    uint             `gorm:"not null;index" json:"room_id"`
	UserId    uint             `gorm:"not null;index" json:"user_id"`
	Role      RoomUserRole     `gorm:"type:varchar(20);not null;default:'member'" json:"role"`
	Status    RoomUserStatus   `gorm:"type:varchar(20);not null;default:'normal'" json:"status"`
	CreatedAt time.Time        `json:"created_at"`
	UpdatedAt time.Time        `json:"updated_at"`
	DeletedAt gorm.DeletedAt   `gorm:"index" json:"deleted_at"`

	// 关联关系
	Room *Room `gorm:"foreignKey:RoomId" json:"room,omitempty"`
	User *User `gorm:"foreignKey:UserId" json:"user,omitempty"`
}

// TableName 指定表名
func (RoomUser) TableName() string {
	return "room_users"
}

// IsAdmin 判断是否为管理员
func (ru *RoomUser) IsAdmin() bool {
	return ru.Role == RoomUserRoleAdmin
}

// IsBlocked 判断是否被拉黑
func (ru *RoomUser) IsBlocked() bool {
	return ru.Status == RoomUserStatusBlocked
}
