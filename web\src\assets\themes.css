/* 黑白主题样式 */
:root {
  /* 亮色主题变量 - 纯黑白配色 */
  --color-light-primary: #000000;
  --color-light-secondary: #666666;
  --color-light-background: #ffffff;
  --color-light-surface: #ffffff;
  --color-light-text: #000000;
  --color-light-border: #e5e5e5;
  
  /* 暗色主题变量 - 纯黑白配色 */
  --color-dark-primary: #ffffff;
  --color-dark-secondary: #999999;
  --color-dark-background: #000000;
  --color-dark-surface: #000000;
  --color-dark-text: #ffffff;
  --color-dark-border: #333333;
}

/* 亮色主题 */
.light {
  --color-primary: var(--color-light-primary);
  --color-secondary: var(--color-light-secondary);
  --color-background: var(--color-light-background);
  --color-surface: var(--color-light-surface);
  --color-text: var(--color-light-text);
  --color-border: var(--color-light-border);
}

/* 暗色主题 */
.dark {
  --color-primary: var(--color-dark-primary);
  --color-secondary: var(--color-dark-secondary);
  --color-background: var(--color-dark-background);
  --color-surface: var(--color-dark-surface);
  --color-text: var(--color-dark-text);
  --color-border: var(--color-dark-border);
}

/* 全局样式 */
body {
  background-color: var(--color-background);
  color: var(--color-text);
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* 简洁卡片样式 - 黑白主题 */
.glass-card {
  background: #ffffff;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e5e5;
  transition: all 0.3s ease;
}

/* 暗色主题下的卡片 */
.dark .glass-card {
  background: #000000;
  border: 1px solid #333333;
  box-shadow: 0 2px 8px rgba(255, 255, 255, 0.05);
}

/* 主要按钮样式 - 黑白主题 */
.btn-primary {
  background: #000000;
  transition: all 0.3s ease;
  color: #ffffff;
  font-weight: 500;
  border-radius: 0.5rem;
  padding: 0.75rem 1rem;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 44px;
  border: none;
}

.btn-primary:hover {
  background: #333333;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* 暗色主题下的主要按钮 */
.dark .btn-primary {
  background: #ffffff;
  color: #000000;
}

.dark .btn-primary:hover {
  background: #e5e5e5;
  box-shadow: 0 4px 8px rgba(255, 255, 255, 0.1);
}

/* 工具栏按钮样式 - 黑白主题 */
.toolbar-btn {
  transition: all 0.2s ease;
  border-radius: 0.5rem;
  padding: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: #ffffff;
  border: 1px solid #e5e5e5;
  color: #000000;
}

.toolbar-btn:hover {
  background-color: #f5f5f5;
  transform: scale(1.05);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.dark .toolbar-btn {
  background: #000000;
  border: 1px solid #333333;
  color: #ffffff;
}

.dark .toolbar-btn:hover {
  background-color: #1a1a1a;
  box-shadow: 0 2px 4px rgba(255, 255, 255, 0.05);
}

/* 输入框样式 - 黑白主题 */
.input-field {
  transition: all 0.3s ease;
  border: 1px solid #e5e5e5;
  background-color: #ffffff;
  color: #000000;
  border-radius: 0.5rem;
  padding: 0.75rem 1rem;
  width: 100%;
  height: 44px;
  font-size: 1rem;
}

.input-field:focus {
  border-color: #000000;
  box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.1);
  outline: none;
}

.input-field::placeholder {
  color: #999999;
}

/* 暗色主题下的输入框 */
.dark .input-field {
  border: 1px solid #333333;
  background-color: #000000;
  color: #ffffff;
}

.dark .input-field:focus {
  border-color: #ffffff;
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.1);
}

.dark .input-field::placeholder {
  color: #666666;
}

/* 视频容器样式 */
.video-container {
  aspect-ratio: 16/9;
  border-radius: 0.75rem;
  overflow: hidden;
}

.light .video-container {
  background-color: #e2e8f0;
}

.dark .video-container {
  background-color: #334155;
}

/* 用户列表项样式 */
.user-list-item {
  border-radius: 0.5rem;
  transition: background-color 0.2s ease;
}

.light .user-list-item:hover {
  background-color: #f8fafc;
}

.dark .user-list-item:hover {
  background-color: #334155;
}

/* 聊天消息样式 */
.chat-message {
  animation: fadeIn 0.3s ease;
}

/* 激活标签样式 */
.tab-active {
  border-bottom: 2px solid var(--color-primary);
  color: var(--color-primary);
}


@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
