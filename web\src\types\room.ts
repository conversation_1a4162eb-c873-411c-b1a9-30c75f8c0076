export interface RoomListItem {
  uuid: string
  name: string
  createdAt: string
  hasPassword: boolean
}

export interface CreateRoomRequest {
  name: string
  password?: string
}

export interface JoinRoomRequest {
  password?: string
}

export interface UpdateRoomRequest {
  name?: string
  password?: string
}

export interface RoomMemberInfo {
  userId: number
  userName: string
  role: 'admin' | 'member'
  status: 'normal' | 'blocked'
}